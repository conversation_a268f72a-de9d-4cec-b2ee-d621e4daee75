{
    depfiles = "piano_keyboard.o: src/piano_keyboard.cpp src/piano_keyboard.h\
",
    depfiles_format = "gcc",
    files = {
        "src/piano_keyboard.cpp"
    },
    values = {
        "/nix/store/a0d7m3zn9p2dfa1h7ag9h2wzzr2w25sn-gcc-wrapper-14.2.1.20250322/bin/g++",
        {
            "-m64",
            "-fvisibility=hidden",
            "-fvisibility-inlines-hidden",
            "-O3",
            "-std=c++17",
            "-Ibuild/.gens/pianowo/linux/x86_64/release/platform/windows/idl",
            "-isystem",
            "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include",
            "-isystem",
            "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/imgui",
            "-isystem",
            "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/backends",
            "-isystem",
            "/home/<USER>/.xmake/packages/i/imgui/v1.92.0/48fcfa11eb6f4eb88d02a0796b198e27/include/misc/cpp",
            "-isystem",
            "/nix/store/akn28bf4vh2q3p2czwkm37acmf33bvgd-libglvnd-1.7.0-dev/include",
            "-isystem",
            "/nix/store/spg20iadf5ylh7pl1l9q8ydf7dqghdfi-xorgproto-2024.1/include",
            "-isystem",
            "/nix/store/rngw0q5wr15bzk68kx58xxny4pm19qq5-libXrandr-1.5.4-dev/include",
            "-isystem",
            "/nix/store/zkhis5dlh4iq4x1pcwv58ic9wqm77c0s-libXinerama-1.1.5-dev/include",
            "-isystem",
            "/nix/store/d12h4wl02y0sg1y2gr90az24jrskqjsq-libXcursor-1.2.3-dev/include",
            "-isystem",
            "/nix/store/b2aj478jxny0yvwqr6zglcmp99r3563b-libX11-1.8.12-dev/include",
            "-isystem",
            "/nix/store/iywn2pwh2cmyvq12d5a9jm9xgx86i2sw-libXrender-0.9.12-dev/include",
            "-isystem",
            "/nix/store/3w8gi3kxckdlf1p0dw54b5mwmzcv6zd6-libXi-1.8.2-dev/include",
            "-isystem",
            "/nix/store/4h7vf438jm33r6l9677dc9sm5qkv7sxd-libXfixes-6.0.1-dev/include",
            "-isystem",
            "/nix/store/4dj30iya7h9bfd9aac6vbrr2yviw1ibj-libXext-1.3.6-dev/include",
            "-isystem",
            "/nix/store/wp4nmh0byqn6w9885q10qm6v0vxq8m28-libxcb-1.17.0-dev/include",
            "-isystem",
            "/nix/store/0wwzn4cb4zdj2jahvyirmfx0ksg3kcz7-libXau-1.0.12-dev/include",
            "-DNDEBUG"
        }
    }
}