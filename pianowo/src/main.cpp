#include <iostream>
#include <GLFW/glfw3.h>
#include <GL/gl.h>
#include <imgui.h>
#include <imgui_impl_glfw.h>
#include <imgui_impl_opengl3.h>
#include "piano_keyboard.h"

// Error callback for GLFW
void glfw_error_callback(int error, const char* description) {
    std::cerr << "GLFW Error " << error << ": " << description << std::endl;
}

int main(int argc, char** argv) {
    // Setup GLFW
    glfwSetErrorCallback(glfw_error_callback);
    if (!glfwInit()) {
        std::cerr << "Failed to initialize GLFW" << std::endl;
        return -1;
    }

    // GL 3.0 + GLSL 130
    const char* glsl_version = "#version 130";
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 0);

    // Create window with graphics context
    GLFWwindow* window = glfwCreateWindow(1280, 720, "Piano Keyboard", NULL, NULL);
    if (window == NULL) {
        std::cerr << "Failed to create GLFW window" << std::endl;
        glfwTerminate();
        return -1;
    }
    glfwMakeContextCurrent(window);
    glfwSwapInterval(1); // Enable vsync

    // Setup Dear ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;

    // Setup Dear ImGui style
    ImGui::StyleColorsDark();

    // Setup Platform/Renderer backends
    ImGui_ImplGlfw_InitForOpenGL(window, true);
    ImGui_ImplOpenGL3_Init(glsl_version);

    // Initialize piano keyboard
    PianoKeyboard piano;
    piano.Initialize();

    // Settings variables
    bool show_settings = true;
    bool show_debug = true;
    ImVec4 background_color = ImVec4(0.45f, 0.55f, 0.60f, 1.00f);
    ImVec2 white_key_size = ImVec2(20.0f, 120.0f);
    ImVec2 black_key_size = ImVec2(12.0f, 80.0f);

    // Main loop
    while (!glfwWindowShouldClose(window)) {
        glfwPollEvents();

        // Start the Dear ImGui frame
        ImGui_ImplOpenGL3_NewFrame();
        ImGui_ImplGlfw_NewFrame();
        ImGui::NewFrame();

        // Main menu bar
        if (ImGui::BeginMainMenuBar()) {
            if (ImGui::BeginMenu("View")) {
                ImGui::MenuItem("Settings", NULL, &show_settings);
                ImGui::MenuItem("Debug Info", NULL, &show_debug);
                ImGui::EndMenu();
            }
            if (ImGui::BeginMenu("Piano")) {
                if (ImGui::MenuItem("Reset All Keys")) {
                    for (int i = 0; i < 128; ++i) {
                        piano.SetKeyPressed(i, false);
                    }
                }
                ImGui::EndMenu();
            }
            ImGui::EndMainMenuBar();
        }

        // Update piano keyboard
        piano.Update();
        piano.HandleInput();

        // Get window size for debug info
        int display_w, display_h;
        glfwGetFramebufferSize(window, &display_w, &display_h);

        // Settings window
        if (show_settings) {
            ImGui::Begin("Settings", &show_settings);

            ImGui::Text("Keyboard Settings");
            ImGui::Separator();

            if (ImGui::SliderFloat2("White Key Size", (float*)&white_key_size, 10.0f, 50.0f)) {
                piano.SetWhiteKeySize(white_key_size);
            }

            if (ImGui::SliderFloat2("Black Key Size", (float*)&black_key_size, 5.0f, 30.0f)) {
                piano.SetBlackKeySize(black_key_size);
            }

            ImGui::Text("Display Settings");
            ImGui::Separator();

            ImGui::ColorEdit3("Background Color", (float*)&background_color);

            ImGui::Text("Windows");
            ImGui::Separator();

            ImGui::Checkbox("Show Debug Window", &show_debug);

            if (ImGui::Button("Reset All Keys")) {
                for (int i = 0; i < 128; ++i) {
                    piano.SetKeyPressed(i, false);
                }
            }

            ImGui::End();
        }

        // Piano keyboard window
        ImGui::Begin("Piano Keyboard");
        piano.Render();
        ImGui::End();

        // Debug window
        if (show_debug) {
            ImGui::Begin("Debug Info", &show_debug);
            ImGui::Text("Application average %.3f ms/frame (%.1f FPS)",
                       1000.0f / ImGui::GetIO().Framerate, ImGui::GetIO().Framerate);
            ImGui::Text("Pressed keys: %d", piano.GetPressedKeyCount());

            auto pressed_keys = piano.GetPressedKeys();
            if (!pressed_keys.empty()) {
                ImGui::Text("Active notes:");
                for (int note : pressed_keys) {
                    ImGui::SameLine();
                    ImGui::Text("%d", note);
                }
            }

            ImGui::Text("Mouse Position: (%.1f, %.1f)", ImGui::GetIO().MousePos.x, ImGui::GetIO().MousePos.y);
            ImGui::Text("Window Size: (%d, %d)", display_w, display_h);

            ImGui::End();
        }

        // Rendering
        ImGui::Render();
        glViewport(0, 0, display_w, display_h);
        glClearColor(background_color.x, background_color.y, background_color.z, background_color.w);
        glClear(GL_COLOR_BUFFER_BIT);
        ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());

        glfwSwapBuffers(window);
    }

    // Cleanup
    ImGui_ImplOpenGL3_Shutdown();
    ImGui_ImplGlfw_Shutdown();
    ImGui::DestroyContext();

    glfwDestroyWindow(window);
    glfwTerminate();

    return 0;
}
