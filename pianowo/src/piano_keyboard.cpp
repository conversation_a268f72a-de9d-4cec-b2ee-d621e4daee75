#include "piano_keyboard.h"
#include <algorithm>
#include <cmath>

PianoKeyboard::PianoKeyboard()
    : keyboard_position_(50.0f, 100.0f)
    , keyboard_size_(1200.0f, 200.0f)
    , white_key_size_(20.0f, 120.0f)
    , black_key_size_(12.0f, 80.0f)
    , white_key_color_(Color::FromRGB(255, 255, 255))
    , white_key_pressed_color_(Color::FromRGB(200, 200, 255))
    , black_key_color_(Color::FromRGB(30, 30, 30))
    , black_key_pressed_color_(Color::FromRGB(100, 100, 150))
    , key_border_color_(Color::FromRGB(100, 100, 100))
{
}

PianoKeyboard::~PianoKeyboard() {
}

void PianoKeyboard::Initialize() {
    keys_.clear();
    keys_.reserve(128);
    
    for (int note = 0; note < 128; ++note) {
        PianoKey key;
        key.note = note;
        key.is_black = IsBlackKey(note);
        key.is_pressed = false;
        key.color = key.is_black ? black_key_color_ : white_key_color_;
        keys_.push_back(key);
    }
    
    CalculateKeyPositions();
}

void PianoKeyboard::Update() {
    // Update key colors based on pressed state
    for (auto& key : keys_) {
        if (key.is_pressed) {
            key.color = key.is_black ? black_key_pressed_color_ : white_key_pressed_color_;
        } else {
            key.color = key.is_black ? black_key_color_ : white_key_color_;
        }
    }
}

void PianoKeyboard::Render(OpenGLRenderer& renderer) {
    // Render white keys first (background)
    RenderWhiteKeys(renderer);

    // Render black keys on top
    RenderBlackKeys(renderer);
}

void PianoKeyboard::HandleInput(double mouse_x, double mouse_y, bool mouse_clicked) {
    if (mouse_clicked) {
        Vec2 mouse_pos(mouse_x, mouse_y);
        int clicked_key = GetKeyAtPosition(mouse_pos);

        if (clicked_key >= 0 && clicked_key < 128) {
            SetKeyPressed(clicked_key, !keys_[clicked_key].is_pressed);
        }
    }
}

bool PianoKeyboard::IsKeyPressed(int note) const {
    if (note >= 0 && note < 128) {
        return keys_[note].is_pressed;
    }
    return false;
}

void PianoKeyboard::SetKeyPressed(int note, bool pressed) {
    if (note >= 0 && note < 128) {
        keys_[note].is_pressed = pressed;
    }
}

void PianoKeyboard::SetKeyboardPosition(const Vec2& position) {
    keyboard_position_ = position;
    CalculateKeyPositions();
}

void PianoKeyboard::SetKeyboardSize(const Vec2& size) {
    keyboard_size_ = size;
    CalculateKeyPositions();
}

void PianoKeyboard::SetWhiteKeySize(const Vec2& size) {
    white_key_size_ = size;
    CalculateKeyPositions();
}

void PianoKeyboard::SetBlackKeySize(const Vec2& size) {
    black_key_size_ = size;
    CalculateKeyPositions();
}

int PianoKeyboard::GetPressedKeyCount() const {
    return std::count_if(keys_.begin(), keys_.end(), 
                        [](const PianoKey& key) { return key.is_pressed; });
}

std::vector<int> PianoKeyboard::GetPressedKeys() const {
    std::vector<int> pressed_keys;
    for (const auto& key : keys_) {
        if (key.is_pressed) {
            pressed_keys.push_back(key.note);
        }
    }
    return pressed_keys;
}

bool PianoKeyboard::IsBlackKey(int note) const {
    int octave_note = note % 12;
    return (octave_note == 1 || octave_note == 3 || octave_note == 6 || 
            octave_note == 8 || octave_note == 10);
}

void PianoKeyboard::CalculateKeyPositions() {
    // Calculate white key positions first
    float white_key_x = keyboard_position_.x;
    int white_key_count = 0;

    for (auto& key : keys_) {
        if (!key.is_black) {
            key.position = Vec2(white_key_x, keyboard_position_.y);
            key.size = white_key_size_;
            white_key_x += white_key_size_.x;
            white_key_count++;
        }
    }

    // Calculate black key positions
    for (auto& key : keys_) {
        if (key.is_black) {
            int white_key_index = GetWhiteKeyIndex(key.note);
            if (white_key_index >= 0) {
                float black_key_x = keyboard_position_.x + (white_key_index * white_key_size_.x) - (black_key_size_.x * 0.5f);
                key.position = Vec2(black_key_x, keyboard_position_.y);
                key.size = black_key_size_;
            }
        }
    }
}

int PianoKeyboard::GetWhiteKeyIndex(int note) const {
    int white_key_index = 0;
    for (int i = 0; i < note; ++i) {
        if (!IsBlackKey(i)) {
            white_key_index++;
        }
    }
    return white_key_index;
}

void PianoKeyboard::RenderWhiteKeys(OpenGLRenderer& renderer) {
    for (const auto& key : keys_) {
        if (!key.is_black) {
            renderer.DrawRectWithBorder(key.position, key.size, key.color, key_border_color_);
        }
    }
}

void PianoKeyboard::RenderBlackKeys(OpenGLRenderer& renderer) {
    for (const auto& key : keys_) {
        if (key.is_black) {
            renderer.DrawRectWithBorder(key.position, key.size, key.color, key_border_color_);
        }
    }
}

int PianoKeyboard::GetKeyAtPosition(const Vec2& pos) const {
    // Check black keys first (they're on top)
    for (const auto& key : keys_) {
        if (key.is_black) {
            if (pos.x >= key.position.x && pos.x <= key.position.x + key.size.x &&
                pos.y >= key.position.y && pos.y <= key.position.y + key.size.y) {
                return key.note;
            }
        }
    }

    // Then check white keys
    for (const auto& key : keys_) {
        if (!key.is_black) {
            if (pos.x >= key.position.x && pos.x <= key.position.x + key.size.x &&
                pos.y >= key.position.y && pos.y <= key.position.y + key.size.y) {
                return key.note;
            }
        }
    }

    return -1; // No key found
}
