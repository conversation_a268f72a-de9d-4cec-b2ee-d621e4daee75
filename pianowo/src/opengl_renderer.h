#pragma once

#include <GL/gl.h>
#include <vector>

struct Vec2 {
    float x, y;
    Vec2() : x(0), y(0) {}
    Vec2(float x_, float y_) : x(x_), y(y_) {}
};

struct Color {
    float r, g, b, a;
    Color() : r(1.0f), g(1.0f), b(1.0f), a(1.0f) {}
    Color(float r_, float g_, float b_, float a_ = 1.0f) : r(r_), g(g_), b(b_), a(a_) {}
    
    // Convert from 0-255 range to 0-1 range
    static Color FromRGB(int r, int g, int b, int a = 255) {
        return Color(r / 255.0f, g / 255.0f, b / 255.0f, a / 255.0f);
    }
};

struct Rect {
    Vec2 position;
    Vec2 size;
    Color color;
    Color border_color;
    float border_width;
    
    Rect() : border_width(1.0f) {}
    Rect(Vec2 pos, Vec2 sz, Color col) 
        : position(pos), size(sz), color(col), border_width(1.0f) {}
};

class OpenGLRenderer {
public:
    OpenGLRenderer();
    ~OpenGLRenderer();
    
    // Initialize the renderer
    void Initialize(int window_width, int window_height);
    
    // Set viewport size
    void SetViewport(int width, int height);
    
    // Clear screen
    void Clear(const Color& clear_color);
    
    // Draw a filled rectangle
    void DrawRect(const Vec2& position, const Vec2& size, const Color& color);
    
    // Draw a rectangle with border
    void DrawRectWithBorder(const Vec2& position, const Vec2& size, 
                           const Color& fill_color, const Color& border_color, 
                           float border_width = 1.0f);
    
    // Batch drawing for better performance
    void BeginBatch();
    void AddRect(const Rect& rect);
    void EndBatch();
    
    // Convert screen coordinates to OpenGL coordinates
    Vec2 ScreenToGL(const Vec2& screen_pos) const;
    Vec2 GLToScreen(const Vec2& gl_pos) const;
    
private:
    int window_width_;
    int window_height_;
    std::vector<Rect> batch_rects_;
    
    // Helper functions
    void DrawQuad(const Vec2& position, const Vec2& size);
    void SetupProjection();
};
