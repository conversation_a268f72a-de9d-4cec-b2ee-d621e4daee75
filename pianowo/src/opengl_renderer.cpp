#include "opengl_renderer.h"
#include <iostream>

OpenGLRenderer::OpenGLRenderer() 
    : window_width_(800), window_height_(600) {
}

OpenGLRenderer::~OpenGLRenderer() {
}

void OpenGLRenderer::Initialize(int window_width, int window_height) {
    window_width_ = window_width;
    window_height_ = window_height;
    
    // Enable blending for transparency
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
    
    SetupProjection();
}

void OpenGLRenderer::SetViewport(int width, int height) {
    window_width_ = width;
    window_height_ = height;
    glViewport(0, 0, width, height);
    SetupProjection();
}

void OpenGLRenderer::Clear(const Color& clear_color) {
    glClearColor(clear_color.r, clear_color.g, clear_color.b, clear_color.a);
    glClear(GL_COLOR_BUFFER_BIT);
}

void OpenGLRenderer::DrawRect(const Vec2& position, const Vec2& size, const Color& color) {
    glColor4f(color.r, color.g, color.b, color.a);
    DrawQuad(position, size);
}

void OpenGLRenderer::DrawRectWithBorder(const Vec2& position, const Vec2& size, 
                                       const Color& fill_color, const Color& border_color, 
                                       float border_width) {
    // Draw filled rectangle
    DrawRect(position, size, fill_color);
    
    // Draw border
    glColor4f(border_color.r, border_color.g, border_color.b, border_color.a);
    glLineWidth(border_width);
    
    glBegin(GL_LINE_LOOP);
    glVertex2f(position.x, position.y);
    glVertex2f(position.x + size.x, position.y);
    glVertex2f(position.x + size.x, position.y + size.y);
    glVertex2f(position.x, position.y + size.y);
    glEnd();
}

void OpenGLRenderer::BeginBatch() {
    batch_rects_.clear();
}

void OpenGLRenderer::AddRect(const Rect& rect) {
    batch_rects_.push_back(rect);
}

void OpenGLRenderer::EndBatch() {
    for (const auto& rect : batch_rects_) {
        DrawRectWithBorder(rect.position, rect.size, rect.color, 
                          rect.border_color, rect.border_width);
    }
    batch_rects_.clear();
}

Vec2 OpenGLRenderer::ScreenToGL(const Vec2& screen_pos) const {
    // Convert from screen coordinates (0,0 top-left) to OpenGL coordinates (-1,-1 bottom-left to 1,1 top-right)
    float gl_x = (screen_pos.x / window_width_) * 2.0f - 1.0f;
    float gl_y = 1.0f - (screen_pos.y / window_height_) * 2.0f;
    return Vec2(gl_x, gl_y);
}

Vec2 OpenGLRenderer::GLToScreen(const Vec2& gl_pos) const {
    // Convert from OpenGL coordinates to screen coordinates
    float screen_x = (gl_pos.x + 1.0f) * 0.5f * window_width_;
    float screen_y = (1.0f - gl_pos.y) * 0.5f * window_height_;
    return Vec2(screen_x, screen_y);
}

void OpenGLRenderer::DrawQuad(const Vec2& position, const Vec2& size) {
    glBegin(GL_QUADS);
    glVertex2f(position.x, position.y);
    glVertex2f(position.x + size.x, position.y);
    glVertex2f(position.x + size.x, position.y + size.y);
    glVertex2f(position.x, position.y + size.y);
    glEnd();
}

void OpenGLRenderer::SetupProjection() {
    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    
    // Use screen coordinates (0,0 top-left to width,height bottom-right)
    glOrtho(0.0, window_width_, window_height_, 0.0, -1.0, 1.0);
    
    glMatrixMode(GL_MODELVIEW);
    glLoadIdentity();
}
