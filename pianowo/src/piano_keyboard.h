#pragma once

#include <vector>
#include "opengl_renderer.h"

struct <PERSON><PERSON><PERSON> {
    int note;           // MIDI note number (0-127)
    bool is_black;      // true if black key, false if white key
    bool is_pressed;    // true if key is currently pressed
    Vec2 position;      // Position on screen
    Vec2 size;          // Size of the key
    Color color;        // Current color of the key
};

class PianoKeyboard {
public:
    PianoKeyboard();
    ~PianoKeyboard();

    // Initialize the keyboard with 128 keys (0-127)
    void Initialize();
    
    // Update keyboard state
    void Update();
    
    // Render the keyboard using OpenGL
    void Render(OpenGLRenderer& renderer);

    // Handle mouse input
    void HandleInput(double mouse_x, double mouse_y, bool mouse_clicked);
    
    // Get/Set key state
    bool IsKeyPressed(int note) const;
    void SetKeyPressed(int note, bool pressed);
    
    // Configuration
    void SetKeyboardPosition(const Vec2& position);
    void SetKeyboardSize(const Vec2& size);
    void SetWhiteKeySize(const Vec2& size);
    void SetBlackKeySize(const Vec2& size);
    
    // Get keyboard info
    int GetPressedKeyCount() const;
    std::vector<int> GetPressedKeys() const;

private:
    std::vector<PianoKey> keys_;
    Vec2 keyboard_position_;
    Vec2 keyboard_size_;
    Vec2 white_key_size_;
    Vec2 black_key_size_;

    // Colors
    Color white_key_color_;
    Color white_key_pressed_color_;
    Color black_key_color_;
    Color black_key_pressed_color_;
    Color key_border_color_;
    
    // Helper functions
    bool IsBlackKey(int note) const;
    void CalculateKeyPositions();
    int GetWhiteKeyIndex(int note) const;
    void RenderWhiteKeys(OpenGLRenderer& renderer);
    void RenderBlackKeys(OpenGLRenderer& renderer);
    int GetKeyAtPosition(const Vec2& pos) const;
};
