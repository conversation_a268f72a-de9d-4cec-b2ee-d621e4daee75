#pragma once

#include <vector>
#include <imgui.h>

struct <PERSON><PERSON><PERSON> {
    int note;           // MIDI note number (0-127)
    bool is_black;      // true if black key, false if white key
    bool is_pressed;    // true if key is currently pressed
    ImVec2 position;    // Position on screen
    ImVec2 size;        // Size of the key
    ImU32 color;        // Current color of the key
};

class PianoKeyboard {
public:
    PianoKeyboard();
    ~PianoKeyboard();

    // Initialize the keyboard with 128 keys (0-127)
    void Initialize();
    
    // Update keyboard state
    void Update();
    
    // Render the keyboard using ImGui
    void Render();
    
    // Handle mouse input
    void HandleInput();
    
    // Get/Set key state
    bool IsKeyPressed(int note) const;
    void SetKeyPressed(int note, bool pressed);
    
    // Configuration
    void SetKeyboardPosition(const ImVec2& position);
    void SetKeyboardSize(const ImVec2& size);
    void SetWhiteKeySize(const ImVec2& size);
    void SetBlackKeySize(const ImVec2& size);
    
    // Get keyboard info
    int GetPressedKeyCount() const;
    std::vector<int> GetPressedKeys() const;

private:
    std::vector<PianoKey> keys_;
    ImVec2 keyboard_position_;
    ImVec2 keyboard_size_;
    ImVec2 white_key_size_;
    ImVec2 black_key_size_;
    
    // Colors
    ImU32 white_key_color_;
    ImU32 white_key_pressed_color_;
    ImU32 black_key_color_;
    ImU32 black_key_pressed_color_;
    ImU32 key_border_color_;
    
    // Helper functions
    bool IsBlackKey(int note) const;
    void CalculateKeyPositions();
    int GetWhiteKeyIndex(int note) const;
    void RenderWhiteKeys();
    void RenderBlackKeys();
    int GetKeyAtPosition(const ImVec2& pos) const;
};
