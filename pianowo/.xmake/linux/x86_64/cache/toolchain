{
    cross_arch_x86_64_plat_linux = {
        arch = "x86_64",
        plat = "linux",
        __global = true,
        __checked = false
    },
    cuda_arch_x86_64_plat_linux = {
        arch = "x86_64",
        plat = "linux",
        __global = true,
        __checked = true
    },
    rust_arch_x86_64_plat_linux = {
        arch = "x86_64",
        plat = "linux",
        __global = true,
        __checked = true
    },
    gcc_arch_x86_64_plat_linux = {
        arch = "x86_64",
        plat = "linux",
        __global = true,
        __checked = {
            name = "gcc",
            program = "/run/current-system/sw/bin/gcc"
        }
    },
    gfortran_arch_x86_64_plat_linux = {
        arch = "x86_64",
        plat = "linux",
        __global = true,
        __checked = true
    },
    yasm_arch_x86_64_plat_linux = {
        arch = "x86_64",
        plat = "linux",
        __global = true,
        __checked = true
    },
    go_arch_x86_64_plat_linux = {
        arch = "x86_64",
        plat = "linux",
        __global = true,
        __checked = true
    },
    nasm_arch_x86_64_plat_linux = {
        arch = "x86_64",
        plat = "linux",
        __global = true,
        __checked = true
    },
    fasm_arch_x86_64_plat_linux = {
        arch = "x86_64",
        plat = "linux",
        __global = true,
        __checked = true
    },
    swift_arch_x86_64_plat_linux = {
        arch = "x86_64",
        plat = "linux",
        __global = true,
        __checked = true
    },
    envs_arch_x86_64_plat_linux = {
        arch = "x86_64",
        plat = "linux",
        __global = true,
        __checked = true
    },
    nim_arch_x86_64_plat_linux = {
        arch = "x86_64",
        plat = "linux",
        __global = true,
        __checked = false
    },
    fpc_arch_x86_64_plat_linux = {
        arch = "x86_64",
        plat = "linux",
        __global = true,
        __checked = true
    }
}